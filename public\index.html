<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">

  <title>Babylon.js webpack starter-kit</title>
  <meta name="description" content="A sample project based on Babylon.js, written in typescript, built with webpack">
  <meta name="author" content="<PERSON><PERSON><PERSON>">
    <style>
      html,
      body {
        overflow: hidden;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }

      #renderCanvas {
        width: 100%;
        height: 100%;
        touch-action: none;
      }
    </style>
  </head>

  <body>
    <canvas id="renderCanvas" touch-action="none"></canvas>
  </body>

</html>