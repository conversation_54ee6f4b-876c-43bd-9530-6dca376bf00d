const js = require('@eslint/js');
const globals = require('globals');

module.exports = [
    // Apply to all JavaScript files
    {
        files: ['**/*.js', '**/*.mjs'],
        languageOptions: {
            ecmaVersion: 'latest',
            sourceType: 'module',
            globals: {
                ...globals.browser,
                ...globals.node,
                ...globals.es2021,
            },
        },
        rules: {
            ...js.configs.recommended.rules,
            // Add any custom rules here
            'no-unused-vars': 'warn',
            'no-console': 'off',
        },
    },
    // Configuration for Web Worker files
    {
        files: ['**/navmeshWorker.js', '**/worker.js', '**/*Worker.js'],
        languageOptions: {
            globals: {
                ...globals.worker,
                importScripts: 'readonly',
                Recast: 'readonly',
            },
        },
    },
    // Configuration for test files
    {
        files: ['**/*.test.js', '**/*.spec.js', '**/__tests__/**/*.js'],
        languageOptions: {
            globals: {
                ...globals.jest,
            },
        },
    },
    // Ignore patterns
    {
        ignores: [
            'node_modules/**',
            'dist/**',
            'webpack.*',
            'babel.config.js',
            'jest.config.js',
            'playwright.config.js',
        ],
    },
];
