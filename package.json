{"name": "babylonjs-javascript-webpack-template", "version": "0.0.1", "description": "A simple scene using Babylon.js, javascript and webpack. What could go wrong?", "main": "index.js", "dependencies": {"@babylonjs/assets": "^5.20.0", "@babylonjs/core": "^8.10.0", "@babylonjs/gui-editor": "^8.10.0", "@babylonjs/havok": "^1.3.10", "@babylonjs/inspector": "^8.10.0", "@babylonjs/loaders": "^8.10.0", "@babylonjs/materials": "^8.10.0", "ammo.js": "github:kripken/ammo.js", "recast-detour": "^1.6.4"}, "devDependencies": {"@playwright/test": "^1.52.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@eslint/js": "^9.28.0", "babel-jest": "^29.7.0", "babel-loader": "^9.1.0", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "eslint": "^9.28.0", "file-loader": "^6.2.0", "globals": "^15.0.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "source-map-loader": "^5.0.0", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1", "webpack-merge": "^6.0.1"}, "scripts": {"start": "npx webpack serve --config webpack.dev.cjs", "start:test": "npx webpack serve --config webpack.tests.cjs", "build:dev": "npx webpack --config webpack.dev.cjs", "build": "npx webpack --config webpack.prod.cjs", "lint": "npx eslint . --ext .js,.jsx", "test:visuals": "npx playwright test", "test:unit": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest"}, "repository": {"type": "git", "url": "git+https://github.com/RaananW/babylonjs-webpack-es6.git"}, "keywords": ["Babylon.js", "webpack", "getting started", "javascript", "es6"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.raananweber.com/"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/RaananW/babylonjs-webpack-es6/issues"}, "homepage": "https://github.com/RaananW/babylonjs-webpack-es6"}