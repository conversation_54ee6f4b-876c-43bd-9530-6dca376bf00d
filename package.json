{"name": "babylonjs-typescript-webpack-template", "version": "0.0.1", "description": "A simple scene using Babylon.js, typescript and webpack. What could go wrong?", "main": "index.ts", "dependencies": {"@babylonjs/assets": "^5.20.0", "@babylonjs/core": "^8.10.0", "@babylonjs/gui-editor": "^8.10.0", "@babylonjs/havok": "^1.3.10", "@babylonjs/inspector": "^8.10.0", "@babylonjs/loaders": "^8.10.0", "@babylonjs/materials": "^8.10.0", "ammo.js": "github:kripken/ammo.js", "recast-detour": "^1.6.4"}, "devDependencies": {"@playwright/test": "^1.52.0", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "eslint": "^8.9.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "source-map-loader": "^5.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-shader-loader": "^2.0.2", "typescript": "^5.0.0", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1", "webpack-merge": "^6.0.1"}, "scripts": {"start": "npx webpack serve --config webpack.dev.js", "start:test": "npx webpack serve --config webpack.tests.js", "build:dev": "npx webpack --config webpack.dev.js", "build": "npx webpack --config webpack.prod.js", "lint": "npx eslint . --ext .ts,.tsx", "test:visuals": "npx playwright test", "test:unit": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest"}, "repository": {"type": "git", "url": "git+https://github.com/RaananW/babylonjs-webpack-es6.git"}, "keywords": ["Babylon.js", "webpack", "getting started", "typescript", "es6"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.raananweber.com/"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/RaananW/babylonjs-webpack-es6/issues"}, "homepage": "https://github.com/RaananW/babylonjs-webpack-es6"}